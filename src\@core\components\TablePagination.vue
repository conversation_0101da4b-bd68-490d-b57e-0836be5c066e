<script setup lang="ts">
interface Props {
  page: number
  itemsPerPage: number
  totalItems: number
  showMeta?: boolean
}

interface Emit {
  (e: 'update:page', value: number): void
}

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const updatePage = (value: number) => {
  emit('update:page', value)
}

// 计算总页数，确保至少为1
const totalPages = computed(() => {
  if (props.totalItems === 0)
    return 1

  return Math.ceil(props.totalItems / props.itemsPerPage)
})
</script>

<template>
  <div>
    <VDivider />

    <div class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 px-6 py-3">
      <p
        v-if="showMeta"
        class="text-disabled mb-0"
      >
        {{ paginationMeta({ page: props.page, itemsPerPage: props.itemsPerPage }, props.totalItems) }}
      </p>

      <VPagination
        :model-value="props.page"
        active-color="primary"
        :length="totalPages"
        :total-visible="$vuetify.display.xs ? 1 : Math.min(totalPages, 5)"
        @update:model-value="updatePage"
      />
    </div>
  </div>
</template>
