<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

onMounted(() => {
  getClientInfo()
})

const getClientInfo = async () => {
  const data: any = {
    requestType: 209,
  }

  const res: any = await $api('', data)

  console.log(res)
  if (res.err_code === 0) {
    dealTableData(res.info)
  }
  else { /* empty */ }
}

//   暂时没使用的列表参数
const lastTransitions: any = ref([])

const dealTableData = (data: any) => {
  const { dhcpClient, wireless } = data

  const clients = dhcpClient.map((client: any) => {
    // 初始化连接类型
    let connectionType = 'eth'

    // 检查2.4G网络
    if (wireless?.wifi_2G) {
      const in2G = wireless.wifi_2G.some(
        (w: any) => w.macAddress === client.macAddress,
      )

      if (in2G)
        connectionType = '2G'
    }

    // 检查5G网络
    if (connectionType === 'eth' && wireless?.wifi_5G) {
      const in5G = wireless.wifi_5G.some(
        (w: any) => w.macAddress === client.macAddress,
      )

      if (in5G)
        connectionType = '5G'
    }

    return {
      ipAddress: client.ipAddress,
      macAddress: client.macAddress,
      hostName: client.hostName || '--',
      connectionType,
    }
  })

  lastTransitions.value = clients
}

const moreList = [
  { title: 'Refresh', value: 'refresh' },
  { title: 'Download', value: 'Download' },
  { title: 'View All', value: 'View All' },
]

const getPaddingStyle = (index: number) => index ? 'padding-block-end: 1.25rem;' : 'padding-block: 1.25rem;'
</script>

<template>
  <VCard :title="t('AlarmList')">
    <!--
      <template #append>
      <div class="me-n2">
      <MoreBtn
      size="small"
      :menu-list="moreList"
      />
      </div>
      </template>
    -->

    <VDivider />
    <VTable class="text-no-wrap transaction-table">
      <thead>
        <tr>
          <th>{{ t('Content') }}</th>
          <th>{{ t('DeviceName') }}</th>
          <th>{{ t('Type') }}</th>
          <th>{{ t('Time') }}</th>
        </tr>
      </thead>

      <tbody>
        <tr
          v-for="(transition, index) in lastTransitions"
          :key="index"
        >
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ t('DeviceOffline') }}
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ t('Device1') }}
            </div>
          </td>

          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ t('EventType1') }}
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              10, Jan 2020 20:07
            </div>
          </td>
        </tr>
      </tbody>
    </VTable>
  </VCard>
</template>

<style lang="scss">
.transaction-table {
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > td,
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > th {
    border-block-end: none !important;
  }
}

.download {
  color: var(--Color-Primary-primary-500, #4080ff);

  /* Basic Typography/caption */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.4px;
  line-height: 18px;

  /* 138.462% */
}

.upload {
  color: var(--Color-Success-success-500, #28c76f);

  /* Basic Typography/caption */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.4px;
  line-height: 18px;

  /* 138.462% */
}
</style>
