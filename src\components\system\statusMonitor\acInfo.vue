<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const timer: any = ref(null)

onMounted(() => {
  // 清除定时器
  clearInterval(timer.value)
  timer.value = null

  // 加载请求
  getNetworkInfo()
  getApOnline()
  timer.value = setInterval(() => {
    getNetworkInfo()
    getApOnline()
  }, 15000)
})
onUnmounted(() => {
  // 清除定时器
  clearInterval(timer.value)
  timer.value = null
})

const acInfo: any = ref({})
const acNumber: any = ref({})

// 计算主机名：AC200 + MAC地址后四位
const computedHostname = computed(() => {
  if (acInfo.value.mac) {
    const macAddress = acInfo.value.mac.replace(/[:-]/g, '').toUpperCase()
    const lastFourDigits = macAddress.slice(-4)

    return `${acInfo.value.hostname}-${lastFourDigits}`
  }

  return '--'
})

async function getApOnline() {
  const data: any = await $api('', { requestType: 509 })
  if (data.err_code === 0)
    acNumber.value = data.info
}

async function getNetworkInfo() {
  const data = await $api('', { requestType: 200 })
  if (data.info.mode.systemMode == '3' || data.info.w4GInfo.internetStatus == '0') {
    // todo lte show
  }
  else {
    // todo lte hide
  }
  if (data.err_code === 0) {
    if (data.err_code == 0) {
      if (data.info.mode.systemMode != undefined && data.info.mode.systemMode != '') {
        sessionStorage.setItem('systemMode', data.info.mode.systemMode)
        acInfo.value.wanLinkStatus = data.info.wanInfo.wanLinkStatus

        // 设置mac地址
        if (data.info.wanInfo.wanMacAddress != undefined && data.info.wanInfo.wanMacAddress != '') {
          acInfo.value.mac = data.info.wanInfo.wanMacAddress
          sessionStorage.setItem('macAddress', data.info.wanInfo.wanMacAddress)
        }
        else if (data.info.network.wan.wanMacAddress != undefined && data.info.network.wan.wanMacAddress != '') {
          acInfo.value.mac = data.info.network.wan.wanMacAddress
          sessionStorage.setItem('macAddress', data.info.network.wan.wanMacAddress)
        }

        if (data.info.mode.systemMode == '0' || data.info.mode.systemMode == '1' || data.info.mode.systemMode == '3' || data.info.mode.systemMode == '4') {
          if (data.info.wanInfo.wanIpAddress != undefined && data.info.wanInfo.wanIpAddress != '' && data.info.wanInfo.wanIpAddress != '0.0.0.0')
            acInfo.value.wanIP = data.info.wanInfo.wanIpAddress
        }

        if (data.info.wanInfo.wanNetmask == '0.0.0.0')
          acInfo.value.wanNetmask = '-'
        else
          acInfo.value.wanNetmask = data.info.wanInfo.wanNetmask

        if (data.info.wanInfo.wanGateway == '0.0.0.0')
          acInfo.value.wanGateway = '-'
        else
          acInfo.value.wanGateway = data.info.wanInfo.wanGateway

        if (data.info.wanInfo.wanDns[0] == '0.0.0.0' || data.info.wanInfo.wanGateway == '0.0.0.0')
          acInfo.value.wanDns1 = '-'
        else
          acInfo.value.wanDns1 = data.info.wanInfo.wanDns[0]

        if (data.info.wanInfo.wanDns[1] == '0.0.0.0')
          acInfo.value.wanDns2 = '-'
        else
          acInfo.value.wanDns2 = data.info.wanInfo.wanDns[1]
      }
      if (data.info.system.hostName != undefined && data.info.system.hostName != '')
        acInfo.value.hostname = data.info.system.hostName

      if (data.info.system.SN != undefined && data.info.system.SN != '')
        acInfo.value.sn = data.info.system.SN

      if (data.info.system.firmwareVersion != undefined && data.info.system.firmwareVersion != '')
        acInfo.value.firmware = data.info.system.firmwareVersion

      if (data.info.system.date != undefined && data.info.date != '')
        acInfo.value.localtime = data.info.system.date

      if (data.info.system.upTime != undefined && data.info.system.upTime != '') {
        acInfo.value.upTime = data.info.system.upTime
        getTime(acInfo.value.upTime)
      }

      // wan tx rx
      if (data.info.wanInfo.tx_sum != undefined && data.info.wanInfo.tx_sum != '')
        acInfo.value.tx = data.info.wanInfo.tx_sum

      if (data.info.wanInfo.rx_sum != undefined && data.info.wanInfo.rx_sum != '')
        acInfo.value.rx = data.info.wanInfo.rx_sum

      if (data.info.network.lan.lanIpAddress != undefined && data.info.network.lan.lanIpAddress != '')
        acInfo.value.lanIP = data.info.network.lan.lanIpAddress

      if (data.info.network.lan.lanNetmask != undefined && data.info.network.lan.lanNetmask != '')
        acInfo.value.lanNetmask = data.info.network.lan.lanNetmask

      if (data.info.dhcp.dhcpMaxNumber != undefined && data.info.dhcp.dhcpMaxNumber != '')
        acInfo.value.dhcpMaxNumber = data.info.dhcp.dhcpMaxNumber

      if (data.info.dhcp.dhcpLeaseTime != undefined && data.info.dhcp.dhcpLeaseTime != '')
        acInfo.value.dhcpLeaseTime = data.info.dhcp.dhcpMaxNumber

      // if (data.info.wireless.wifi_2G.wifiSsid_2G != undefined && data.info.wireless.wifi_2G.wifiSsid_2G != '') {
      //   acInfo.wifiSsid_2G = data.info.wireless.wifi_2G.wifiSsid_2G
      // }
      // if (data.info.wireless.wifi_2G.wifiAuthmode_2G != undefined && data.info.wireless.wifi_2G.wifiAuthmode_2G != '') {
      //   if (data.info.wireless.wifi_2G.wifiAuthmode_2G == 'none') {
      //   } else {
      //     acInfo.wifiAuthmode_2G = data.info.wireless.wifi_2G.wifiAuthmode_2G
      //     if (data.info.wireless.wifi_2G.wifikey_2g != undefined) {
      //       acInfo.wifikey_2g = data.info.wireless.wifi_2G.wifikey_2g
      //     }
      //   }
      // }
      // if (data.info.wlanInfo.wlanChannel.wifiChannel_2G != undefined && data.info.wlanInfo.wlanChannel.wifiChannel_2G != '') {
      //   acInfo.wifiChannel_2G = data.info.wlanInfo.wlanChannel.wifiChannel_2G
      // }
      // if (data.info.wlanInfo.wlanRate.wifiRate_2G != undefined && data.info.wlanInfo.wlanRate.wifiRate_2G != '') {
      //   acInfo.wifiRate_2G = data.info.wlanInfo.wlanRate.wifiRate_2G
      // }
      // if (data.info.wireless.wifi_5G.wifiSsid_5G != undefined && data.info.wireless.wifi_5G.wifiSsid_5G != '') {
      //   acInfo.wifiSsid_5G = data.info.wireless.wifi_5G.wifiSsid_5G
      // }
      // if (data.info.wireless.wifi_5G.wifiAuthmode_5G != undefined && data.info.wireless.wifi_5G.wifiAuthmode_5G != '') {
      //   if (data.info.wireless.wifi_5G.wifiAuthmode_5G == 'none') {
      //   } else {
      //     acInfo.wifiAuthmode_5G = data.info.wireless.wifi_5G.wifiAuthmode_5G
      //     if (data.info.wireless.wifi_5G.wifiKey_5G != undefined) {
      //       acInfo.wifiKey_5G = data.info.wireless.wifi_5G.wifiKey_5G
      //     }
      //   }
      // }
    }

    // if (data.info.wlanInfo.wlanChannel.wifiChannel_5G != undefined && data.info.wlanInfo.wlanChannel.wifiChannel_5G != '') {
    //   acInfo.wifiChannel_5G = data.info.wlanInfo.wlanChannel.wifiChannel_5G
    // }
    // if (data.info.wlanInfo.wlanRate.wifiRate_5G != undefined && data.info.wlanInfo.wlanRate.wifiRate_5G != '') {
    //   acInfo.wifiRate_5G = data.info.wlanInfo.wlanRate.wifiRate_5G
    // }
    if (data.info.w4GInfo != undefined && data.info.w4GInfo != '') {
      if (data.info.w4GInfo.internetStatus != undefined && data.info.w4GInfo.internetStatus != '') {
        if (data.info.w4GInfo.internetStatus == '0') {
        }
        else if (data.info.w4GInfo.internetStatus == '1') { /* empty */ }
      }
      if (data.info.w4GInfo.networkOperator != undefined && data.info.w4GInfo.networkOperator != '')
        acInfo.value.operators = data.info.w4GInfo.networkOperator
      else
        acInfo.value.operators = '-'

      if (data.info.w4GInfo.flow != undefined && data.info.w4GInfo.flow != '')
        acInfo.value.traffic = `${data.info.w4GInfo.flow} MB`
      else
        acInfo.value.traffic = '-'

      if (data.info.w4GInfo.signal != undefined && data.info.w4GInfo.signal != '')
        acInfo.value.signal = `${data.info.w4GInfo.signal} dbm`
      else
        acInfo.value.signal = '-'

      if (data.info.w4GInfo.IMEI != undefined && data.info.w4GInfo.IMEI != '')
        acInfo.value.imei = data.info.w4GInfo.IMEI
      else
        acInfo.value.imei = '-'

      if (data.info.w4GInfo.ICCID != undefined && data.info.w4GInfo.ICCID != '')
        acInfo.value.iccid = data.info.w4GInfo.ICCID
      else
        acInfo.value.iccid = '-'
    }
  }
}

function getTime(upTime: number) {
  let days = Math.floor(upTime / 86400)
  upTime %= 86400
  let hours = Math.floor(upTime / 3600)
  upTime %= 3600
  let minutes = Math.floor(upTime / 60)
  let seconds = upTime % 60
  days = Number.parseInt(days)
  hours = Number.parseInt(hours)
  minutes = Number.parseInt(minutes)
  seconds = Number.parseInt(seconds)
  acInfo.value.upTime = `${days} ${t('SystemStatus.Days')} ${hours}:${minutes}:${seconds}`
}
</script>

<template>
  <VRow class="match-height">
    <!-- 👉 主机名 -->
    <VCol
      cols="12"
      md="4"
    >
      <div class="infobox">
        <div class="infoLabel">
          {{ t('SystemStatus.Hostname') || '--' }}
        </div>
        <div class="infoContent">
          {{ computedHostname }}
        </div>
      </div>
    </VCol>

    <!-- 👉 AC 型号 -->
    <VCol
      cols="12"
      md="4"
    >
      <div class="infobox">
        <div class="infoLabel">
          {{ t('SystemStatus.Model') || '--' }}
        </div>
        <div class="infoContent">
          {{ acInfo.hostname || '--' }}
        </div>
      </div>
    </VCol>

    <!-- 👉 AC 固件版本 -->
    <VCol
      cols="12"
      md="4"
    >
      <div class="infobox">
        <div class="infoLabel">
          {{ t('SystemStatus.FirmwareVersion') || '--' }}
        </div>
        <div class="infoContent">
          {{ acInfo.firmware || '--' }}
        </div>
      </div>
    </VCol>

    <!-- 👉 AC IP地址 -->
    <VCol
      cols="12"
      md="4"
    >
      <div class="infobox">
        <div class="infoLabel">
          {{ t('SystemStatus.IPAddress') || '--' }}
        </div>
        <div class="infoContent">
          {{ acInfo.wanIP || '--' }}
        </div>
      </div>
    </VCol>
    <!-- 👉 AC 运行时间 -->
    <VCol
      cols="12"
      md="4"
    >
      <div class="infobox">
        <div class="infoLabel">
          {{ t('SystemStatus.RunningTime') || '--' }}
        </div>
        <div class="infoContent">
          {{ acInfo.upTime || '--' }}
        </div>
      </div>
    </VCol>

    <!-- 👉 AC MAC地址 -->
    <VCol
      cols="12"
      md="4"
    >
      <div class="infobox">
        <div class="infoLabel">
          {{ t('SystemStatus.MACAddress') || '--' }}
        </div>
        <div class="infoContent">
          {{ acInfo.mac || '--' }}
        </div>
      </div>
    </VCol>
    <!-- 👉 AC AP数量 -->
    <VCol
      cols="12"
      md="4"
    >
      <div class="infobox">
        <div class="infoLabel">
          {{ t('SystemStatus.APCount') || '--' }}
        </div>
        <div class="infoContent">
          {{ acNumber.sum || '--' }}
        </div>
      </div>
    </VCol>

    <!-- 👉 AC 序列号 -->
    <VCol
      cols="12"
      md="4"
    >
      <div class="infobox">
        <div class="infoLabel">
          {{ t('SystemStatus.SerialNumber') || '--' }}
        </div>
        <div class="infoContent">
          {{ acInfo.sn || '--' }}
        </div>
      </div>
    </VCol>

    <VCol cols="12">
      <div class="netMain">
        <div class="netBox">
          <div class="netUp">
            <div class="netImg">
              <svg
                fill="none"
                height="44"
                viewBox="0 0 44 44"
                width="44"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M22 40.3334C32.1252 40.3334 40.3333 32.1253 40.3333 22.0001C40.3333 11.8749 32.1252 3.66675 22 3.66675C11.8748 3.66675 3.66666 11.8749 3.66666 22.0001C3.66666 32.1253 11.8748 40.3334 22 40.3334Z"
                  stroke="black"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2.5"
                />
                <path
                  d="M22 3.66675C17.2925 8.6097 14.6667 15.1741 14.6667 22.0001C14.6667 28.8261 17.2925 35.3905 22 40.3334C26.7076 35.3905 29.3334 28.8261 29.3334 22.0001C29.3334 15.1741 26.7076 8.6097 22 3.66675Z"
                  stroke="black"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2.5"
                />
                <path
                  d="M3.66666 22H40.3333"
                  stroke="black"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2.5"
                />
              </svg>
            </div>
            <div class="netDesc">
              {{ t('SystemStatus.NetworkAccess') }}
            </div>
            <div class="netIP">
              {{ acInfo.wanIP || '--' }}
            </div>
            <div
              v-if="acInfo.wanLinkStatus == 0"
              class="netText text-green"
            >
              {{ t('Internet') }}
            </div>
            <div
              v-else-if="acInfo.wanLinkStatus == 1 || acInfo.wanLinkStatus == 2 || acInfo.wanLinkStatus == 3"
              class="netText text-error"
            >
              {{ t('NoInternet') }}
            </div>
            <div
              v-else
              class="netText text-error"
            />
          </div>
          <div
            v-if="acInfo.wanLinkStatus == 1 || acInfo.wanLinkStatus == 0"
            class="netLine"
          >
            <svg
              fill="none"
              height="12"
              viewBox="0 0 116 12"
              width="116"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0.46967 5.46967C0.176777 5.76256 0.176777 6.23744 0.46967 6.53033L5.24264 11.3033C5.53553 11.5962 6.01041 11.5962 6.3033 11.3033C6.59619 11.0104 6.59619 10.5355 6.3033 10.2426L2.06066 6L6.3033 1.75736C6.59619 1.46447 6.59619 0.989593 6.3033 0.696699C6.01041 0.403806 5.53553 0.403806 5.24264 0.696699L0.46967 5.46967ZM115.53 6.53033C115.823 6.23744 115.823 5.76256 115.53 5.46967L110.757 0.696699C110.464 0.403806 109.99 0.403806 109.697 0.696699C109.404 0.989593 109.404 1.46447 109.697 1.75736L113.939 6L109.697 10.2426C109.404 10.5355 109.404 11.0104 109.697 11.3033C109.99 11.5962 110.464 11.5962 110.757 11.3033L115.53 6.53033ZM1 6.75H115V5.25H1V6.75Z"
                fill="#28C76F"
              />
            </svg>
          </div>
          <div
            v-else-if="acInfo.wanLinkStatus == 2 || acInfo.wanLinkStatus == 3"
            class="netLine"
          >
            <svg
              width="116"
              height="12"
              viewBox="0 0 116 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0.46967 5.46967C0.176777 5.76256 0.176777 6.23744 0.46967 6.53033L5.24264 11.3033C5.53553 11.5962 6.01041 11.5962 6.3033 11.3033C6.59619 11.0104 6.59619 10.5355 6.3033 10.2426L2.06066 6L6.3033 1.75736C6.59619 1.46447 6.59619 0.989592 6.3033 0.696699C6.01041 0.403806 5.53553 0.403806 5.24264 0.696699L0.46967 5.46967ZM115.53 6.53033C115.823 6.23744 115.823 5.76256 115.53 5.46967L110.757 0.696699C110.464 0.403806 109.99 0.403806 109.697 0.696699C109.404 0.989592 109.404 1.46447 109.697 1.75736L113.939 6L109.697 10.2426C109.404 10.5355 109.404 11.0104 109.697 11.3033C109.99 11.5962 110.464 11.5962 110.757 11.3033L115.53 6.53033ZM1 6V6.75H115V6V5.25H1V6Z"
                fill="#FF4C51"
              />
            </svg>
          </div>
          <div
            v-else
            class="netLine"
          >
            <svg
              width="116"
              height="12"
              viewBox="0 0 116 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0.46967 5.46967C0.176777 5.76256 0.176777 6.23744 0.46967 6.53033L5.24264 11.3033C5.53553 11.5962 6.01041 11.5962 6.3033 11.3033C6.59619 11.0104 6.59619 10.5355 6.3033 10.2426L2.06066 6L6.3033 1.75736C6.59619 1.46447 6.59619 0.989592 6.3033 0.696699C6.01041 0.403806 5.53553 0.403806 5.24264 0.696699L0.46967 5.46967ZM115.53 6.53033C115.823 6.23744 115.823 5.76256 115.53 5.46967L110.757 0.696699C110.464 0.403806 109.99 0.403806 109.697 0.696699C109.404 0.989592 109.404 1.46447 109.697 1.75736L113.939 6L109.697 10.2426C109.404 10.5355 109.404 11.0104 109.697 11.3033C109.99 11.5962 110.464 11.5962 110.757 11.3033L115.53 6.53033ZM1 6V6.75H115V6V5.25H1V6Z"
                fill="#97939E"
              />
            </svg>
          </div>
          <div class="netUp">
            <div class="netImg">
              <svg
                fill="none"
                height="44"
                viewBox="0 0 44 44"
                width="44"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M40.3334 22H3.66669"
                  stroke="black"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2.8"
                />
                <path
                  d="M9.99169 9.36825L3.66669 21.9999V32.9999C3.66669 33.9724 4.053 34.905 4.74063 35.5926C5.42826 36.2803 6.36089 36.6666 7.33335 36.6666H36.6667C37.6391 36.6666 38.5718 36.2803 39.2594 35.5926C39.947 34.905 40.3334 33.9724 40.3334 32.9999V21.9999L34.0084 9.36825C33.7048 8.75736 33.2368 8.24327 32.6571 7.88376C32.0774 7.52426 31.4088 7.33361 30.7267 7.33325H13.2734C12.5912 7.33361 11.9227 7.52426 11.3429 7.88376C10.7632 8.24327 10.2952 8.75736 9.99169 9.36825Z"
                  stroke="black"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2.8"
                />
                <path
                  d="M11 29.3333H11.0183"
                  stroke="black"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2.8"
                />
                <path
                  d="M18.3333 29.3333H18.3516"
                  stroke="black"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2.8"
                />
              </svg>
            </div>
            <div class="netDesc">
              {{ acInfo.hostname || '--' }}
            </div>
            <div class="netIP">
              {{ acInfo.lanIP || '--' }}
            </div>
            <div class="netText text-blue">
              {{ t('Local') }}
            </div>
          </div>
        </div>
      </div>
    </VCol>
  </VRow>
</template>

<style lang="scss" scoped>
@use "@core/scss/template/libs/apex-chart";

.infoLabel {
  font-family: "PingFang SC";
  font-size: 13px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 20px;
  vertical-align: middle;
}

.infoContent {
  font-family: "PingFang SC";
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 22px;
  vertical-align: middle;
}

.netMain {
  inline-size: 100%;
}

.netBox {
  display: flex;
  align-items: center;
  justify-content: center;
  block-size: 140px;
  inline-size: 350px;
  margin-block: 0;
  margin-inline: auto;

  .netUp {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    inline-size: 100px;

    .netImg {
      block-size: 44px;
      inline-size: 44px;
      inset-inline-start: 28px;
    }

    .netDesc {
      font-family: "PingFang SC";
      font-size: 13px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 20px;
      text-align: center;
    }

    .netIP {
      font-family: "PingFang SC";
      font-size: 13px;
      font-weight: 400;
      letter-spacing: 0;
      line-height: 20px;
      text-align: center;
    }

    .netText {
      font-family: "PingFang SC";
      font-size: 13px;
      font-weight: 400;
      letter-spacing: 0;
      line-height: 20px;
      text-align: center;
    }

    .text-blue {
      color: var(--Color-Primary-primary-400, #6aa1ff);
    }

    .text-green {
      color: var(--Color-Palette-success-main, #28c76f);
    }
  }

  .netLine {
    flex: 1;
    margin-block: 0;
    margin-inline: 20px;
  }
}

// .v-theme--light {
//  color-scheme: normal;
//  --v-theme-bg: 248, 247, 250;
// }
// .v-theme--dark {
//  color-scheme: normal;
//  --v-theme-bg: 37, 41, 60;
// }

.v-theme--light {
  color-scheme: normal;

  .netMain {
    background: #f8f7fa !important;
  }
}

.v-theme--dark {
  .netMain {
    background: #25293c !important;
  }
}
</style>
