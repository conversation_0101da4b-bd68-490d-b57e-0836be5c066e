<script lang="ts" setup>
import { useTheme } from 'vuetify'
import { getColumnChartConfig } from '@/@core/libs/apex-chart/apexCharConfig'
import { DATE_FILTER_OPTIONS } from '@/utils/constants'

const vuetifyTheme = useTheme()

const userDataSeries = [
  {
    name: 'Apple',
    data: [90, 120, 55, 100, 80, 125],
  },
]

const userChartConfig = computed(() => {
  return {
    ...getColumnChartConfig(vuetifyTheme.current.value),
    chart: {
      stacked: true,
      parentHeightOffset: 0,
      toolbar: { show: false },
    },
    legend: {
      show: true,
      fontSize: '14px',
      position: 'right',
      formatter(seriesName: string, opts: any) {
        return `<div class="ml-2">
          <p class="mb-1" style="line-height: 22px;color: rgb(var(--v-theme-on-surface))">${seriesName}</p>
          <p class="mb-0 font-weight-medium	" style="font-size: 18px;">${opts.w.globals.series[0][opts.seriesIndex]}%</p>
        </div>`
      },
      width: 300,
      horizontalAlign: 'left',
      markers: {
        width: 10,
        height: 10,
        radius: 5,
        offsetX: 0,
        offsetY: 0,
        shape: 'circle',
      },
    },
    colors: ['#4080FF', '#00BAD1', '#28C76F', '#808390', '#FF4C51', '#FF9F43'],
    tooltip: {
      custom({ series, seriesIndex, dataPointIndex, w }: any) {
        return `
            <div class="chart-tooltip">
              <p class="chart-tooltip-title mb-2">${w.globals.labels[dataPointIndex]}</p>
              <p class="chart-tooltip-label">占比</p>
              <p class="chart-tooltip-date mb-2">${series[0][dataPointIndex]}%</p>
              <p class="chart-tooltip-label">数量</p>
              <p class="chart-tooltip-value text-primary">${series[0][dataPointIndex]}</p>
            </div>
          `
      },
    },
    plotOptions: {
      bar: {
        horizontal: true,
        columnWidth: '15%',
        borderRadius: 8,
        borderRadiusApplication: 'end',
        borderRadiusWhenStacked: 'all',
        distributed: true,

      },
    },
    xaxis: {
      type: 'category',
      categories: [
        '连接成功',
        '信号质量差',
        'IP分配失败',
        'ACL拦截',
        '超最大并发限制',
        'AP/AC异常',
      ],
      labels: {
        formatter(value: any) {
          const date = new Date(value)

          return `${date.getHours()}:${date.getMinutes()}`
        },

      },
    },
  }
})

const productsData = ref({
  total: 3,
  products: [
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: true,
    },
  ],
})

const headers = [
  { title: '终端名称', key: 'name' },
  { title: 'MAC地址', key: 'sn' },
  { title: '厂商', key: 'no' },
  { title: 'SSID', key: 'ip' },
  { title: '关联设备名称', key: 'ip' },
  { title: 'VLAN ID', key: 'ip' },
  { title: '无线频段', key: 'ip' },
  { title: '失败阶段', key: 'ip' },
  { title: '连接开始时间', key: 'ip' },
]

const totalProduct = computed(() => productsData.value.products.length + 100)
const page = ref(1)

const date = ref(1)
</script>

<template>
  <div class="wireless">
    <VRow>
      <VCol cols="8">
        <VCard class="mb-6">
          <template #title>
            <div
              style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
            >
              <div class="mr-2 text-h5">
                无线连接统计
              </div>
              <div>
                <DateSelector
                  v-model:current-value="date"
                  :items="DATE_FILTER_OPTIONS"
                  item-title="title"
                  item-value="value"
                />
              </div>
            </div>
          </template>
          <div class="px-6 py-4">
            <VueApexCharts
              class="horizontal-chart"
              type="bar"
              height="286"
              :options="userChartConfig"
              :series="userDataSeries"
            />
          </div>
        </VCard>
      </VCol>
      <VCol cols="4">
        <VCard class="mb-6">
          <template #title>
            <div
              style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
            >
              <div class="mr-2 text-h5">
                AP连接失败率排行
              </div>
              <div>
                <DateSelector
                  v-model:current-value="date"
                  :items="DATE_FILTER_OPTIONS"
                  item-title="title"
                  item-value="value"
                />
              </div>
            </div>
          </template>
          <VDivider />
          <div class="connection-failed-table">
            <div class="connection-failed-header px-6 py-4">
              <div class="d-flex align-center justify-space-between">
                <div class="connection-failed-header_name">
                  AP名称
                </div>
                <div class="connection-failed-header_name text-end">
                  失败次数
                </div>
              </div>
            </div>
            <div class="connection-failed-body pa-6">
              <div
                v-for="item in 10"
                class="connection-failed-body_row d-flex align-center justify-space-between"
              >
                <div class="connection-failed-body_row_col">
                  <div class="source-name">
                    USR-AP1
                  </div>
                  <div class="sub-info">
                    失败率32%
                  </div>
                </div>
                <div class="connection-failed-body_row_col">
                  52
                </div>
              </div>
            </div>
          </div>
        </VCard>
      </VCol>
    </VRow>
    <VCard class="mb-6">
      <template #title>
        <div class="text-h5">
          无线终端关联失败列表
        </div>
      </template>
      <VDivider />
      <VDataTableServer
        :items="productsData.products"
        :headers="headers"
        :items-length="totalProduct"
        :no-data-text="t('NoData')"
        disable-sort
      >
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="10"
            :total-items="totalProduct"
            :show-meta="false"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </div>
</template>

<style lang="scss">
.horizontal-chart {
  .apexcharts-canvas {}

  .apexcharts-legend {
    display: grid !important;
    grid-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
    padding-block: 20px !important;
    padding-inline: 10px !important;
    row-gap: 20px;

    &-series {
      align-items: flex-start !important;
    }

    &-marker {
      margin-block-start: 3px;
    }
  }
}

.connection-failed-table {
  block-size: 318px;
  inline-size: 100%;

  .connection-failed-header {
    border-block-end: 1px solid rgba(var(--v-theme-secondary), 0.12);

    &-name {
      color: rgb(var(--v-theme-on-surface));
      font-size: 15px;
      line-height: 22px;
    }
  }

  .connection-failed-body {
    overflow: auto;
    block-size: 100%;

    &_row {
      margin-block-end: 16px;

      &:last-child {
        margin-block-end: 0;
      }

      &_col {
        &:last-child {
          font-size: 15px;
          font-weight: 500;
          text-align: end;
        }
      }
    }

    .source-name {
      color: rgba(var(--v-theme-on-surface), 0.9);
      font-size: 15px;
      line-height: 22px;
    }

    .sub-info {
      color: rgba(var(--v-theme-on-surface), 0.4);
      font-size: 13px;
      line-height: 18px;
    }
  }
}
</style>
