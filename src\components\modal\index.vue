<template>
  <VDialog
    :model-value="dialog"
    :width="400"
    persistent
  >
    <VCard class="pa-2 pa-sm-10" title="提示信息">
      <VCardText>
        <div>{{ tipMessage }}</div>
        <label
          v-if="showCountdown"
          id="show_count_time"
          style="font-size: 2.5rem; color: #007bff"
        >
          {{ countdown }}s
        </label>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<script lang="ts" setup>
import { onUnmounted, ref } from 'vue'

const tipMessage = ref('')
const dialog = ref(false)
const countdown = ref(0)
const showCountdown = ref(false)
let timer: number | null = null

const open = (message: string, timeout: number, url?: string) => {
  console.log('[Modal] Open called:', { message, timeout, url })
  // 初始化状态
  dialog.value = true
  countdown.value = Math.floor(timeout / 1000)
  showCountdown.value = timeout >= 5000
  tipMessage.value = message
  // 清除旧计时器
  if (timer) clearInterval(timer)

  // 倒计时逻辑
  if (showCountdown.value) {
    timer = window.setInterval(() => {
      countdown.value -= 1
      if (countdown.value <= 0) {
        clearInterval(timer as number)
      }
    }, 1000)
  }

  // 自动关闭逻辑
  setTimeout(() => {
    close()
    if (url) {
      window.location.replace(url)
    } else {
      window.location.reload()
    }
  }, timeout)
}

const close = () => {
  console.log('[Modal] Closing')
  dialog.value = false
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

// 必须明确暴露方法
defineExpose({
  open,
  close,
})

onUnmounted(() => {
  if (timer) clearInterval(timer)
})
</script>
