<script lang="ts" setup>
import ApLoad from '@/components/network/ApLoad.vue';
import RadioFreQuency from '@/components/network/RadioFrequency.vue';

const currentTab = ref(0)
const tabs = ref([{
  label: 'AP负载',
  value: 0
}, {
  label: '射频统计',
  value: 1
},
  // {
  //   label: '无线连接',
  //   value: 2
  // }
])
</script>

<template>
  <div>
    <VTabs v-model="currentTab" class="v-tabs-pill mb-4">
      <VTab v-for="item in tabs" :key="item.value" :value="item.value">{{ item.label }}</VTab>
    </VTabs>

    <VWindow v-model="currentTab">
      <VWindowItem v-for="item in tabs" :key="item.value">
        <ApLoad v-if="item.value === 0" />
        <RadioFreQuency v-if="item.value === 1" />
        <!-- <Wireless v-if="item.value === 2" /> -->
      </VWindowItem>
    </VWindow>
  </div>
</template>

<style lang="scss"></style>
