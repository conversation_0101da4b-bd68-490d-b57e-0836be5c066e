import { createApp } from 'vue'

import App from '@/App.vue'
import { registerPlugins } from '@core/utils/plugins'
import waitingModalPlugin from '@/components/modal/index';

// Styles
import '@core/scss/template/index.scss'
import '@styles/styles.scss'
import 'element-plus/dist/index.css'
// Create vue app
const app = createApp(App)

// Register plugins
registerPlugins(app)
app.use(waitingModalPlugin);
// Mount vue app
app.mount('#app')
