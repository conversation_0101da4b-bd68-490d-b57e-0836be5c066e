<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>有人物联 · 商用网络</title>
  <link rel="stylesheet" type="text/css" href="/loader.css" />
</head>

<body>
  <div id="app">
    <div id="loading-bg">
      <div class="loading-logo">
        <!-- SVG Logo -->
        <svg width="69" height="49" viewBox="0 0 69 49" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_1481_28475)">
            <path d="M34.5203 33.0946C31.7583 33.7868 30.9572 36.1497 30.9572 36.1497L28.967 42.183H24.4438L26.1925 32.5852L30.9572 28.0033L25.9525 22.3356L19.6316 29.8288L16.25 25.6182L26.3781 16.2604C27.9203 19.0545 31.6899 20.1766 33.8253 20.0133C39.4134 19.6239 41.3073 15.1216 41.3073 15.1216L52.9567 15.818V19.0964L43.0714 19.3238L43.0909 25.5596L44.8438 30.1973C47.3294 31.2119 50.0105 33.657 50.7739 37.2424C51.3461 39.2898 51.5694 43.513 51.5694 43.513H47.1899C46.3092 37.7784 43.772 35.0457 43.772 35.0457C43.772 35.0457 40.8663 31.5971 34.5203 33.0974V33.0946Z" fill="#0F39A8"/>
            <path d="M41.1193 11.5015C41.1193 15.6745 37.7167 19.0547 33.5214 19.0547C29.3261 19.0547 25.918 15.6745 25.918 11.5015C25.918 7.32855 29.3163 3.94971 33.5214 3.94971C37.7265 3.94971 41.1193 7.32994 41.1193 11.5015Z" fill="#FE5C1C"/>
          </g>
          <defs>
            <clipPath id="clip0_1481_28475">
              <rect width="68" height="48" fill="white" transform="translate(0.609375 0.355957)"/>
            </clipPath>
          </defs>
        </svg>
      </div>
      <div class=" loading">
        <div class="effect-1 effects"></div>
        <div class="effect-2 effects"></div>
        <div class="effect-3 effects"></div>
      </div>
    </div>
  </div>
  <script type="module" src="/src/main.ts"></script>
  <script>
    const loaderColor = localStorage.getItem('vuexy-initial-loader-bg') || '#FFFFFF'
    const primaryColor = '#0F39A8'

    if (loaderColor)
      document.documentElement.style.setProperty('--initial-loader-bg', loaderColor)
    if (loaderColor)
      document.documentElement.style.setProperty('--initial-loader-bg', loaderColor)

    if (primaryColor)
      document.documentElement.style.setProperty('--initial-loader-color', primaryColor)
    </script>
  </body>
</html>
