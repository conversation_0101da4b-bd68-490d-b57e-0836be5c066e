<script lang="ts" setup>
import { useTheme } from 'vuetify'
import { getColumnChartConfig } from '@/@core/libs/apex-chart/apexCharConfig'
import { DATE_FILTER_OPTIONS } from '@/utils/constants'

const vuetifyTheme = useTheme()

const userDataSeries = [
  {
    name: 'Apple',
    data: [90, 120, 55, 100, 80, 125, 175, 70, 88],
  },
]

const userChartConfig = computed(() => {
  return {
    ...getColumnChartConfig(vuetifyTheme.current.value),
    colors: ['#53D28C'],
    chart: {
      offsetX: 10,
      stacked: true,
      parentHeightOffset: 0,
      toolbar: { show: false },
    },
    tooltip: {
      custom({ series, seriesIndex, dataPointIndex, w }: any) {
        return `
            <div class="chart-tooltip">
              <p class="chart-tooltip-title mb-2">在线用户</p>
              <p class="chart-tooltip-label">时间</p>
              <p class="chart-tooltip-value mb-2">${w.globals.labels[seriesIndex]}</p>
              <p class="chart-tooltip-label">在线用户数量</p>
              <p class="chart-tooltip-value text-primary">${series[seriesIndex][dataPointIndex]}</p>
            </div>
          `
      },
    },
    legend: {},
    plotOptions: {
      bar: {
        columnWidth: '15%',
        borderRadius: 10,
        borderRadiusApplication: 'end',
        borderRadiusWhenStacked: 'all',
      },
    },
    xaxis: {
      type: 'category',
      categories: [
        '2023-10-01T01:00:00.000Z',
        '2023-10-02T02:00:00.000Z',
        '2023-10-03T03:00:00.000Z',
        '2023-10-04T04:00:00.000Z',
        '2023-10-05T05:00:00.000Z',
        '2023-10-06T06:00:00.000Z',
        '2023-10-07T07:00:00.000Z',
        '2023-10-08T08:00:00.000Z',
        '2023-10-09T09:00:00.000Z',
      ],
      labels: {
        formatter(value: any) {
          const date = new Date(value)

          return `${date.getHours()}:${date.getMinutes()}`
        },

      },
    },
  }
})

const productsData = ref({
  total: 3,
  products: [
    {
      name: 'AP',
      version: 'v2.7.4',
      no: 'AP310i',
      sn: '30801524053000002628',
      time: '2025-05-01 23:33:44',
      ip: '***************',
      mac: 'f3:24:4f:g5:a4:6h',
      status: true,
    },
  ],
})

const headers = [
  { title: '名称', key: 'name' },
  { title: '上行(MB)', key: 'sn' },
  { title: '下行(MB)', key: 'no' },
  { title: '总计(MB)', key: 'ip' },
]

const totalProduct = computed(() => productsData.value.products.length + 100)
const page = ref(1)

const date = ref(1)
</script>

<template>
  <div class="ap-load">
    <VCard class="mb-6">
      <template #title>
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
        >
          <div class="mr-2 text-h5">
            AP终端设备负载
          </div>
          <div>
            <DateSelector
              v-model:current-value="date"
              :items="DATE_FILTER_OPTIONS"
              item-title="title"
              item-value="value"
            />
          </div>
        </div>
      </template>
      <VCardText>
        <VueApexCharts
          type="bar"
          height="400"
          :options="userChartConfig"
          :series="userDataSeries"
        />
      </VCardText>
    </VCard>
    <VRow>
      <VCol cols="6">
        <VCard class="mb-6">
          <template #title>
            <div
              style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
            >
              <div class="mr-2 text-h5">
                AP流量排行
              </div>
              <div>
                <DateSelector
                  v-model:current-value="date"
                  :items="DATE_FILTER_OPTIONS"
                  item-title="title"
                  item-value="value"
                />
              </div>
            </div>
          </template>
          <VDataTableServer
            :items="productsData.products"
            :headers="headers"
            :items-length="totalProduct"
            :no-data-text="t('NoData')"
            disable-sort
          >
            <template #bottom>
              <TablePagination
                v-model:page="page"
                :items-per-page="10"
                :total-items="totalProduct"
                :show-meta="false"
              />
            </template>
          </VDataTableServer>
        </VCard>
      </VCol>
      <VCol cols="6">
        <VCard class="mb-6">
          <template #title>
            <div
              style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
            >
              <div class="mr-2 text-h5">
                AP负载量排行
              </div>
              <div>
                <DateSelector
                  v-model:current-value="date"
                  :items="DATE_FILTER_OPTIONS"
                  item-title="title"
                  item-value="value"
                />
              </div>
            </div>
          </template>
          <VDataTableServer
            :items="productsData.products"
            :headers="headers"
            :items-length="totalProduct"
            :no-data-text="t('NoData')"
            disable-sort
          >
            <template #bottom>
              <TablePagination
                v-model:page="page"
                :items-per-page="10"
                :total-items="totalProduct"
                :show-meta="false"
              />
            </template>
          </VDataTableServer>
        </VCard>
      </VCol>
    </VRow>
  </div>
</template>
