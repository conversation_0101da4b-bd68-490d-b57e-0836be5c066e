<script lang="ts" setup>
import moment from 'moment'
import { useI18n } from 'vue-i18n'
import { useTheme } from 'vuetify'
import { getAreaChartSplineConfig } from '@core/libs/apex-chart/apexCharConfig'

const props = defineProps({
  mainList: {
    type: Array,
    default: () => [],
  },
})

const { t } = useI18n()

const timeList = ref<string[]>([])
const uploadList = ref<number[]>([])
const downloadList = ref<number[]>([])

// 添加单位标识符
const uploadUnit = ref(t('NetworkTraffic.Units.BytesPerSecond'))
const downloadUnit = ref(t('NetworkTraffic.Units.BytesPerSecond'))

// 格式化网络流量，动态调整单位
function formatDataRate(bytes: number) {
  if (bytes === 0)
    return { value: 0, unit: t('NetworkTraffic.Units.BytesPerSecond') }

  const units = [
    t('NetworkTraffic.Units.BytesPerSecond'),
    t('NetworkTraffic.Units.KBPerSecond'),
    t('NetworkTraffic.Units.MBPerSecond'),
    t('NetworkTraffic.Units.GBPerSecond'),
  ]

  // 标准的单位选择逻辑
  let unitIndex = 0
  let value = bytes

  // 打印调试信息
  console.log('原始值(B):', bytes)

  // 小于1KB的用B表示
  if (bytes < 1024) {
    unitIndex = 0
    value = bytes
  }

  // 1KB-1MB的用KB表示
  else if (bytes >= 1024 && bytes < 1048576) {
    unitIndex = 1
    value = bytes / 1024
  }

  // 1MB-1GB的用MB表示
  else if (bytes >= 1048576 && bytes < 1073741824) {
    unitIndex = 2
    value = bytes / 1048576
  }

  // 大于1GB的用GB表示
  else {
    unitIndex = 3
    value = bytes / 1073741824
  }

  // 保留两位小数
  const formattedValue = Number.parseFloat(value.toFixed(2))

  console.log('转换后的值:', formattedValue, '单位:', units[unitIndex])

  return { value: formattedValue, unit: units[unitIndex] }
}

// 确定最佳显示单位
function determineBestUnit(dataList: number[]) {
  if (dataList.length === 0)
    return t('NetworkTraffic.Units.BytesPerSecond')

  // 找出最大值
  const maxValue = Math.max(...dataList)

  return formatDataRate(maxValue).unit
}

// 转换数据为指定单位
function convertToUnit(bytes: number, targetUnit: string) {
  const units = [
    t('NetworkTraffic.Units.BytesPerSecond'),
    t('NetworkTraffic.Units.KBPerSecond'),
    t('NetworkTraffic.Units.MBPerSecond'),
    t('NetworkTraffic.Units.GBPerSecond'),
  ]

  const unitIndex = units.indexOf(targetUnit)
  if (unitIndex === -1)
    return bytes

  let result = bytes

  // B到指定单位的转换
  if (unitIndex === 0) { // 转为B
    result = bytes
  }
  else if (unitIndex === 1) { // 转为KB
    result = bytes / 1024
  }
  else if (unitIndex === 2) { // 转为MB
    result = bytes / 1048576 // 1024*1024
  }
  else if (unitIndex === 3) { // 转为GB
    result = bytes / 1073741824 // 1024*1024*1024
  }

  return Number.parseFloat(result.toFixed(2))
}

// watch 监听uploadList和downloadList,动态生成表格数据
watch(props.mainList, (newMainList: any) => {
  let arr = JSON.parse(JSON.stringify(newMainList))
  arr = arr.reverse()

  // 清空之前的数据
  timeList.value = []
  uploadList.value = []
  downloadList.value = []

  // 记录最大值，用于调试
  let maxUpload = 0
  let maxDownload = 0

  for (const i in arr) {
    const rxRate = arr[i].rx_rate // 接收速率 = 下载
    const txRate = arr[i].tx_rate // 发送速率 = 上传

    // 更新最大值，与下面的赋值保持一致
    maxUpload = Math.max(maxUpload, txRate) // 上传 = 发送 = tx_rate
    maxDownload = Math.max(maxDownload, rxRate) // 下载 = 接收 = rx_rate

    uploadList.value.push(txRate) // 上传列表使用发送速率
    downloadList.value.push(rxRate) // 下载列表使用接收速率

    const time = moment(arr[i].timestamp).format('HH:mm')

    timeList.value.push(time)
  }

  // 输出调试信息
  console.log('最大上传速率(B/s):', arr)
  console.log('选择的上传单位:', uploadList.value)
  console.log('选择的下载单位:', downloadList.value)

  // 确定最佳显示单位
  uploadUnit.value = determineBestUnit(uploadList.value)
  downloadUnit.value = determineBestUnit(downloadList.value)

  // 转换数据为对应单位
  uploadList.value = uploadList.value.map(value => convertToUnit(value, uploadUnit.value))
  downloadList.value = downloadList.value.map(value => convertToUnit(value, downloadUnit.value))
  console.log('选择的上传单位:', uploadList.value)
  console.log('选择的下载单位:', downloadList.value)
}, { immediate: true })

const vuetifyTheme = useTheme()

onMounted(() => {
})

// const chartConfig = computed(() => getAreaChartSplineConfig(vuetifyTheme.current.value))
const chartConfig = computed(() => ({
  ...getAreaChartSplineConfig(vuetifyTheme.current.value),

  // 新增平滑曲线配置
  stroke: {
    curve: 'smooth', // 核心平滑配置
    width: 3, // 适当增加线宽
  },

  // 调整填充透明度
  fill: {
    type: 'solid', // 核心修改点
    opacity: 0.2, // 统一透明度
  },
  legend: {
    show: true,
    position: 'bottom', // 位置在底部
    horizontalAlign: 'center', // 水平居中
    itemMargin: {
      horizontal: 8, // 图例项水平间距
      vertical: 4, // 图例项垂直间距
    },
    markers: {
      width: 10, // 颜色标记宽度
      height: 10, // 颜色标记高度
      radius: 5, // 圆角半径
      offsetY: 1, // 垂直偏移量
    },
  },
  colors: [ // 显式定义颜色序列
    '#28c76f', // 上传颜色
    '#4080ff', // 下载颜色
  ],
  xaxis: {
    categories: timeList.value,
  },
  tooltip: {
    y: {
      formatter(value: number, { seriesIndex }: { seriesIndex: number }) {
        // 根据数据系列返回对应的单位
        const unit = seriesIndex === 0 ? uploadUnit.value : downloadUnit.value

        return `${value} ${unit}`
      },
    },
  },
}))

// 修改 series 为计算属性（关键修改）
const series = computed(() => [
  {
    name: `${t('NetworkTraffic.Upload')}`,
    data: uploadList.value,
  },
  {
    name: `${t('NetworkTraffic.Download')}`,
    data: downloadList.value,
  },
])
</script>

<template>
  <VueApexCharts
    type="area"
    height="330"
    :options="chartConfig"
    :series="series"
  />
</template>
