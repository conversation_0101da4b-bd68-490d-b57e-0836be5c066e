import { useTheme } from 'vuetify'

// composable function to return the image variant as per the current theme and skin
export const useGenerateImageVariant = (imgLight: string, imgDark: string) => {
  const { global } = useTheme()

  return computed(() => {
    if (global.name.value === 'light')
      return imgLight

    if (global.name.value === 'dark')
      return imgDark

    return imgLight
  })
}
