<script lang="ts" setup>
import moment from 'moment'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface Props {
  notifications: any[]
  badgeProps?: object
  location?: any
}
interface Emit {
  (e: 'read', value: number[]): void
  (e: 'ignore', value: number[]): void
  (e: 'click:notification', value: Notification): void
}

const props = withDefaults(defineProps<Props>(), {
  location: 'bottom end',
  badgeProps: undefined,
})

const emit = defineEmits<Emit>()

const totalUnseenNotifications = computed(() => {
  return props.notifications.filter(item => item.isRead == 0).length
})

const toggleReadUnread = (Id: number) => {
  console.log('toggleReadUnread', Id)
  emit('read', Id)
}

const ignoreNotification = (Id: number) => {
  console.log('ignoreNotification', Id)
  emit('ignore', Id)
}

const formatTime = (time: string) => {
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}
</script>

<template>
  <IconBtn id="notification-btn">
    <VBadge
      v-bind="props.badgeProps"
      :model-value="props.notifications.some(n => n.isRead == 0)"
      color="error"
      dot
      offset-x="2"
      offset-y="3"
    >
      <VIcon icon="tabler-bell" />
    </VBadge>

    <VMenu
      activator="parent"
      width="380px"
      :location="props.location"
      offset="12px"
      :close-on-content-click="false"
    >
      <VCard class="d-flex flex-column">
        <!-- 👉 Header -->
        <VCardItem class="notification-section">
          <VCardTitle class="text-h6">
            {{ t('Notifications') }}
          </VCardTitle>

          <template #append>
            <VChip
              v-show="props.notifications.some(n => !n.isSeen)"
              size="small"
              color="primary"
              class="me-2"
            >
              {{ totalUnseenNotifications }} {{ t('NewNotifications') }}
            </VChip>
          </template>
        </VCardItem>

        <VDivider />

        <!-- 👉 Notifications list -->
        <PerfectScrollbar
          :options="{ wheelPropagation: false }"
          style="max-block-size: 23.75rem;"
        >
          <VList class="notification-list rounded-0 py-0">
            <template
              v-for="(notification, index) in props.notifications"
              :key="notification.title"
            >
              <VDivider v-if="index > 0" />
              <VListItem
                link
                lines="one"
                min-height="66px"
                class="list-item-hover-class"
                @click="$emit('click:notification', notification)"
              >
                <div>
                  <div>
                    <div class="d-flex align-center mb-1">
                      <VIcon
                        v-if="notification.isRead == 0"
                        class="mr-2"
                        size="10"
                        icon="tabler-circle-filled"
                        color="primary"
                        @click.stop="toggleReadUnread(notification.event_id)"
                      />
                      <p
                        v-if="notification.event_name == 0"
                        class="text-sm font-weight-medium mb-0"
                      >
                        {{ t('APOnline') }}
                      </p>
                      <p
                        v-if="notification.event_name == 1"
                        class="text-sm font-weight-medium mb-0"
                      >
                        {{ t('APOffline') }}
                      </p>
                      <p
                        v-if="notification.event_name == 3"
                        class="text-sm font-weight-medium mb-0"
                      >
                        {{ t('APHighTemperature') }}
                      </p>
                    </div>
                    <p
                      v-if="notification.event_name == 0"
                      class="text-body-2 mb-2"
                      style=" letter-spacing: 0.4px !important; line-height: 18px;"
                    >
                      {{ t('APOnline') }}
                    </p>
                    <p
                      v-if="notification.event_name == 1"
                      class="text-body-2 mb-2"
                      style=" letter-spacing: 0.4px !important; line-height: 18px;"
                    >
                      {{ t('APOffline') }}
                    </p>
                    <p
                      v-if="notification.event_name == 3"
                      class="text-body-2 mb-2"
                      style=" letter-spacing: 0.4px !important; line-height: 18px;"
                    >
                      {{ t('HighTemperatureWarning') }}
                    </p>
                    <div class="d-flex align-center justify-space-between">
                      <p
                        class="text-sm text-disabled mb-0"
                        style=" letter-spacing: 0.4px !important; line-height: 18px;"
                      >
                        {{ formatTime(notification.timestamp) }}
                      </p>
                      <p
                        v-if="notification.status == 0"
                        class="text-sm text-primary"
                        @click="ignoreNotification(notification.event_id)"
                      >
                        {{ t('IgnoreAlert') }}
                      </p>
                      <p
                        v-if="notification.status == 1"
                        class="text-sm"
                      >
                        {{ t('Ignored') }}
                      </p>
                    </div>
                  </div>
                </div>
              </VListItem>
            </template>

            <VListItem
              v-show="!props.notifications.length"
              class="text-center text-medium-emphasis"
              style="block-size: 56px;"
            >
              <VListItemTitle>{{ t('NoEvents') }}</VListItemTitle>
            </VListItem>
          </VList>
        </PerfectScrollbar>

        <!-- <VDivider /> -->

        <!-- 👉 Footer -->
        <!--
          <VCardText
          v-show="props.notifications.length"
          class="pa-4"
          >
          <VBtn
          block
          size="small"
          >
          View All Notifications
          </VBtn>
          </VCardText>
        -->
      </VCard>
    </VMenu>
  </IconBtn>
</template>

<style lang="scss">
.notification-section {
  padding-block: 0.75rem;
  padding-inline: 1rem;
}

.list-item-hover-class {
  .visible-in-hover {
    display: none;
  }

  &:hover {
    .visible-in-hover {
      display: block;
    }
  }
}

.notification-list.v-list {
  .v-list-item {
    border-radius: 0 !important;
    margin: 0 !important;
    padding-block: 0.75rem !important;
  }
}

// Badge Style Override for Notification Badge
.notification-badge {
  .v-badge__badge {
    /* stylelint-disable-next-line liberty/use-logical-spec */
    min-width: 18px;
    padding: 0;
    block-size: 18px;
  }
}
</style>
