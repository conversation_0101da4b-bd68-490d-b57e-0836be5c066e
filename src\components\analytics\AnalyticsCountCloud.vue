<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const logisticData = ref([
  { icon: 'tabler-truck', color: 'primary', title: t('AC'), value: 1, isHover: false, online: 0, offline: 0 },
  { icon: 'tabler-alert-triangle', color: 'warning', title: t('AP'), value: 8, isHover: false, online: 0, offline: 0 },
  { icon: 'tabler-git-fork', color: 'error', title: t('Router'), value: 0, isHover: false, online: 0, offline: 0 },
  { icon: 'tabler-truck', color: 'primary', title: t('TerminalCount'), value: 42, isHover: false, online: 0, offline: 0 },
])

onMounted(() => {
  getAllInfo()
  getClientInfo()
})

const getAllInfo = async () => {
  const data: any = {
    requestType: '',
  }

  const res: any = await $api('', data)

  console.log(res)
  if (res.err_code === 0) {
    logisticData.value[1].value = res.info.sum
    logisticData.value[1].online = res.info.online
    logisticData.value[1].offline = res.info.offline
  }
  else { /* empty */ }
}

const getClientInfo = async () => {
  const data: any = {
    requestType: 209,
  }

  const res: any = await $api('', data)

  console.log(res)
  if (res.err_code === 0) {
    logisticData.value[3].value = res.info.dhcpClinet?.length || 0 + res.info.wireless?.wifi_2G?.length || 0 + res.info.wireless?.wifi_5G?.length || 0
  }
  else { /* empty */ }
}
</script>

<template>
  <VRow>
    <VCol
      v-for="(data, index) in logisticData"
      :key="index"
      cols="12"
      md="3"
    >
      <div>
        <VCard
          class="logistics-card-statistics cursor-pointer"
          style="border: 0;"
          @mouseenter="data.isHover = true"
          @mouseleave="data.isHover = false"
        >
          <VCardText>
            <div class="d-flex align-center gap-x-4 mb-1">
              <VAvatar
                variant="tonal"
                :color="data.color"
                rounded
              >
                <VIcon
                  :icon="data.icon"
                  size="28"
                />
              </VAvatar>
              <h4 class="text-h4">
                {{ data.value }} {{ t('UnitDevice') }}
              </h4>
            </div>
            <div class="statusBox mb-1 mt-1">
              {{ t('Offline') }} {{ data.offline }}
            </div>
            <div class="text-body-1 descText">
              {{ data.title }}
            </div>
          </VCardText>
        </VCard>
      </div>
    </VCol>
  </VRow>
</template>

<style lang="scss" scoped>
@use "@core/scss/base/mixins" as mixins;

.statusBox {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--Border-Radius-border-radius-sm, 4px);
  background-color: rgba(var(--v-theme-on-surface), var(--v-activated-opacity));
  block-size: 24px;
  color: var(--Light-Text-Primary, text-primary);
  font-family: "PingFang HK";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px !important;
  font-style: normal;
  font-weight: 500;
  inline-size: 56px;
}

.descText {
  color: var(text-primary);

  /* Basic Typography/h6 */
  font-family: "PingFang HK";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 15px !important;
  font-style: normal;
  font-weight: 500;
  line-height: 22px; /* 146.667% */
}

.logistics-card-statistics {
  border-block-end-style: solid;
  border-block-end-width: 2px;

  &:hover {
    border-block-end-width: 3px;
    margin-block-end: -1px;

    @include mixins.elevation(8);

    transition: all 0.1s ease-out;
  }
}

.skin--bordered {
  .logistics-card-statistics {
    border-block-end-width: 2px;

    &:hover {
      border-block-end-width: 3px;
      margin-block-end: -2px;
      transition: all 0.1s ease-out;
    }
  }
}
</style>
