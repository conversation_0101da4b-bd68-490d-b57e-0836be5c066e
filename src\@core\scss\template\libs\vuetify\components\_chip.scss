// 👉 Chip
.v-chip {
  line-height: 1.25rem;

  .v-chip__close {
    margin-inline: 4px -6px !important;

    .v-icon {
      opacity: 0.7;
    }
  }

  .v-chip__content {
    .v-icon {
      block-size: 20px;
      font-size: 20px;
      inline-size: 20px;
    }
  }

  &:not(.v-chip--variant-elevated) {
    color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
  }

  &.v-chip--variant-elevated {
    background-color: rgba(var(--v-theme-on-surface), var(--v-activated-opacity));
    color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
  }

  &:not([class*="text-"]) {
    --v-activated-opacity: 0.08;
  }

  // common style for all sizes
  &.v-chip--size-default,
  &.v-chip--size-small {
    .v-icon--start,
    .v-chip__filter {
      margin-inline-start: 0 !important;
    }

    &:not(.v-chip--pill) {
      .v-avatar--start {
        margin-inline: -6px 4px;
      }

      .v-avatar--end {
        margin-inline: 4px -6px;
      }
    }
  }

  // small size
  &:not(.v-chip--pill).v-chip--size-small {
    --v-chip-height: 24px;

    &.v-chip--label {
      border-radius: 0.25rem;
    }

    font-size: 13px;

    .v-avatar {
      --v-avatar-height: 16px;
    }

    .v-chip__close {
      font-size: 16px;
      max-block-size: 16px;
      max-inline-size: 16px;
    }
  }

  // extra small size
  &:not(.v-chip--pill).v-chip--size-x-small {
    --v-chip-height: 20px;

    &.v-chip--label {
      border-radius: 0.25rem;
      padding-inline: 0.625rem;
    }

    font-size: 13px;

    .v-avatar {
      --v-avatar-height: 16px;
    }

    .v-chip__close {
      font-size: 16px;
      max-block-size: 16px;
      max-inline-size: 16px;
    }
  }

  // default size
  &:not(.v-chip--pill).v-chip--size-default {
    .v-avatar {
      --v-avatar-height: 20px;
    }
  }
}
