<script setup lang="ts">
import { useTheme } from 'vuetify'
import { hexToRgb } from '@layouts/utils'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const vuetifyTheme = useTheme()
const todayAlarm = ref(0)
const totalAlarm = ref(0)
const timer = ref(null)

const series = ['88']

const chartOptions = computed(() => {
  const currentTheme = vuetifyTheme.current.value.colors
  const variableTheme = vuetifyTheme.current.value.variables

  return {
    labels: [t('TodayAlarm')],
    chart: {
      type: 'radialBar',
    },
    plotOptions: {
      radialBar: {
        offsetY: 10,
        startAngle: -140,
        endAngle: 130,
        hollow: {
          size: '65%',
        },
        track: {
          background: currentTheme.surface,
          strokeWidth: '100%',
        },
        dataLabels: {
          name: {
            offsetY: -20,
            color: `rgba(${hexToRgb(currentTheme['on-surface'])},${variableTheme['disabled-opacity']})`,
            fontSize: '13px',
            fontWeight: '400',
            fontFamily: 'Public Sans',
          },
          value: {
            offsetY: 10,
            color: `rgba(${hexToRgb(currentTheme['on-background'])},${variableTheme['high-emphasis-opacity']})`,
            fontSize: '38px',
            fontWeight: '500',
            fontFamily: 'Public Sans',
            formatter: () => `${todayAlarm.value}`,
          },
        },
      },
    },
    colors: ['#28C76F'],
    fill: {
      type: 'gradient',
      gradient: {
        shade: 'dark',
        shadeIntensity: 0.5,
        gradientToColors: ['#28C76F'],
        inverseColors: true,
        opacityFrom: 1,
        opacityTo: 0.6,
        stops: [30, 70, 100],
      },
    },
    stroke: {
      dashArray: 10,
    },
    grid: {
      padding: {
        top: -20,
        bottom: 5,
      },
    },
    states: {
      hover: {
        filter: {
          type: 'none',
        },
      },
      active: {
        filter: {
          type: 'none',
        },
      },
    },
    responsive: [
      {
        breakpoint: 960,
        options: {
          chart: {
            height: 280,
          },
        },
      },
    ],
  }
})

const supportTicket = reactive([
  {
    avatarColor: 'error',
    title: t('CriticalEvent'),
    subtitle: 0,
  },
  {
    avatarColor: 'primary',
    title: t('HighEvent'),
    subtitle: 0,
  },
  {
    avatarColor: 'info',
    title: t('MediumEvent'),
    subtitle: 0,
  },
  {
    avatarColor: 'warning',
    title: t('LowEvent'),
    subtitle: 0,
  },
])

const dealTap = () => {
  console.log('dealTap')
}

const resetData = () => {
  $api('', { requestType: 523 }).then(res => {
    if (res.err_code === 0) {
      const data = res.info.event_summary || {}

      todayAlarm.value = data.TodayTotal
      supportTicket[0].subtitle = data.Critical
      supportTicket[1].subtitle = data.High
      supportTicket[2].subtitle = data.Medium
      supportTicket[3].subtitle = data.Minor
    }
  })
}

onMounted(() => {
  resetData()
  timer.value = setInterval(() => {
    resetData()
  }, 10000)
})
onUnmounted(() => {
  clearInterval(timer.value)
  timer.value = null
})

const router = useRouter()

const toEvent = () => {
  router.push({
    path: '/network/event',
  })
}
</script>

<template>
  <VCard>
    <VCardItem>
      <template #title>
        <div class="d-flex align-start justify-space-between">
          <div class="mr-2 text-h5">
            <VCardTitle>{{ t('AlarmList') }}</VCardTitle>
            <VCardSubtitle>{{ t('DeviceEventCount') }}</VCardSubtitle>
          </div>
          <div>
            <VBtn
              variant="tonal"
              @click="toEvent"
            >
              {{ t('View') }}
            </VBtn>
          </div>
        </div>
      </template>
    </VCardItem>

    <VCardText>
      <VRow>
        <VCol
          cols="12"
          lg="4"
          md="4"
        >
          <VList class="card-list mt-7">
            <VListItem
              v-for="ticket in supportTicket"
              :key="ticket.title"
              @click="dealTap"
            >
              <VListItemTitle class="font-weight-medium">
                {{ ticket.title }}
              </VListItemTitle>
              <VListItemSubtitle>
                {{ ticket.subtitle }}
              </VListItemSubtitle>
              <template #prepend>
                <VAvatar
                  rounded
                  size="34"
                  :color="ticket.avatarColor"
                  variant="tonal"
                  class="me-1"
                >
                  <VIcon
                    size="22"
                    icon="tabler-urgent"
                  />
                </VAvatar>
              </template>
            </VListItem>
          </VList>
        </VCol>
        <VCol
          cols="12"
          lg="8"
          md="8"
        >
          <VueApexCharts
            :key="todayAlarm"
            :options="chartOptions"
            :series="series"
            :append-series="t('CountUnit')"
            height="360"
          />
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
</template>

<style lang="scss" scoped>
.card-list {
  --v-card-list-gap: 16px;
}
</style>
