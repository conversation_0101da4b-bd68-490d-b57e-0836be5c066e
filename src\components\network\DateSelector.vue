<script lang="ts" setup>
const compProps = defineProps({
  items: {
    type: Array<Record<string, any>>,
    default: () => [],
    required: true
  },
  'itemValue': {
    type: String,
    default: 'value'
  },
  'itemTitle': {
    type: String,
    default: 'title'
  },
  currentValue: {
    type: [String, Number],
    default: 0
  }
})

const emit = defineEmits(['update:currentValue'])


const itemTitle = computed(() => {
  return compProps['itemTitle']
})

const itemValue = computed(() => {
  return compProps['itemValue']
})

const selectHandle = (item: Record<string, any>) => {
  emit('update:currentValue', compProps.items[item.id][itemValue.value])
}

const text = computed(() => {
  const target = compProps.items.find((item: Record<string, any>) => {
    return item[itemValue.value] === compProps.currentValue
  })
  return target ? target[itemTitle.value] : ''
})


</script>

<template>
  <VMenu>
    <template #activator="{ props }">
      <div class="selector" v-bind="props">
        <div class="selector-label">
          {{ text || '--' }}
        </div>
        <div class="selector-icon">
          <VIcon icon="tabler-chevron-down" color="primary" :size="20"></VIcon>
        </div>
      </div>
    </template>
    <VList :items="items" @click:select="selectHandle" />
  </VMenu>
</template>

<style lang="scss" scoped>
.selector {
  display: flex;
  align-items: center;
  cursor: pointer;

  &-label {
    padding: 6px 14px;
    border-radius: 5px 0 0 5px;
    background: rgba(var(--v-theme-primary), 0.12);
    color: rgb(var(--v-theme-primary));
    font-size: 13px;
    line-height: 18px;
    border-right: 1px solid rgba(var(--v-theme-primary), 0.32);
    height: 30px;
  }

  &-icon {
    height: 28px;
    border-radius: 0 5px 5px 0;
    background: rgba(var(--v-theme-primary), 0.24);

    .v-icon {
      margin: 4.5px;
    }
  }

}
</style>
