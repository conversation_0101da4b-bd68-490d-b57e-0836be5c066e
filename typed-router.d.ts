/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'unplugin-vue-router/types'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    'root': RouteRecordInfo<'root', '/', Record<never, never>, Record<never, never>>,
    '$error': RouteRecordInfo<'$error', '/:error(.*)', { error: ParamValue<true> }, { error: ParamValue<false> }>,
    'config-ap': RouteRecordInfo<'config-ap', '/config/ap', Record<never, never>, Record<never, never>>,
    'config-device': RouteRecordInfo<'config-device', '/config/device', Record<never, never>, Record<never, never>>,
    'config-mode': RouteRecordInfo<'config-mode', '/config/mode', Record<never, never>, Record<never, never>>,
    'device-ap': RouteRecordInfo<'device-ap', '/device/ap', Record<never, never>, Record<never, never>>,
    'device-list': RouteRecordInfo<'device-list', '/device/list', Record<never, never>, Record<never, never>>,
    'device-remote': RouteRecordInfo<'device-remote', '/device/remote', Record<never, never>, Record<never, never>>,
    'login': RouteRecordInfo<'login', '/login', Record<never, never>, Record<never, never>>,
    'network-ap-data': RouteRecordInfo<'network-ap-data', '/network/apData', Record<never, never>, Record<never, never>>,
    'network-device-detail': RouteRecordInfo<'network-device-detail', '/network/deviceDetail', Record<never, never>, Record<never, never>>,
    'network-device-status': RouteRecordInfo<'network-device-status', '/network/deviceStatus', Record<never, never>, Record<never, never>>,
    'network-event': RouteRecordInfo<'network-event', '/network/event', Record<never, never>, Record<never, never>>,
    'network-event-detail': RouteRecordInfo<'network-event-detail', '/network/eventDetail', Record<never, never>, Record<never, never>>,
    'network-plc-data': RouteRecordInfo<'network-plc-data', '/network/plcData', Record<never, never>, Record<never, never>>,
    'network-plc-status': RouteRecordInfo<'network-plc-status', '/network/plcStatus', Record<never, never>, Record<never, never>>,
    'network-status': RouteRecordInfo<'network-status', '/network/status', Record<never, never>, Record<never, never>>,
    'network-terminal-detail': RouteRecordInfo<'network-terminal-detail', '/network/terminalDetail', Record<never, never>, Record<never, never>>,
    'network-tupo': RouteRecordInfo<'network-tupo', '/network/tupo', Record<never, never>, Record<never, never>>,
    'system-network-config': RouteRecordInfo<'system-network-config', '/system/networkConfig', Record<never, never>, Record<never, never>>,
    'system-status-monitor': RouteRecordInfo<'system-status-monitor', '/system/statusMonitor', Record<never, never>, Record<never, never>>,
    'system-system-config': RouteRecordInfo<'system-system-config', '/system/systemConfig', Record<never, never>, Record<never, never>>,
    'system-wlan': RouteRecordInfo<'system-wlan', '/system/wlan', Record<never, never>, Record<never, never>>,
  }
}
