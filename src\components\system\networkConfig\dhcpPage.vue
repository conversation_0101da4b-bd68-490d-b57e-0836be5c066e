<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import DhcpList from '@/components/system/list/dhcpList.vue'

const { t } = useI18n()

onMounted(() => {
})

const IPInfo: any = ref({
  static_name: '',
  static_ip: '',
  static_mac: '',
})

const isDialogVisible = ref(false)
const dhcpRef: any = ref(null)
const dhcpIpForm: any = ref(null)

const submitIP = () => {
  dhcpIpForm.value.validate().then(async (result: any) => {
    console.log('validate', result)
    if (result.valid) {
      const data = await $api('', {
        requestType: 601,
        data: {
          dhcp: {
            static_name: IPInfo.value.static_name,
            static_ip: IPInfo.value.static_ip,
            static_mac: IPInfo.value.static_mac,
          },
        },
      })

      if (data.err_code == -1) {
        alert(t('NetworkConfig.DHCP.DHCPBindingError'))
      }
      else {
        isDialogVisible.value = false
        IPInfo.value = {
          static_name: '',
          static_ip: '',
          static_mac: '',
        }
        dhcpRef.value.getDhcpList()
      }
    }
  })
}

const requiredValidator = (value: any) => {
  return !!value || t('NetworkConfig.LAN.Required')
}

// nameValidator: 仅允许英文字母，最长32个字符，不允许空格，支持!@#$%-_+=半角字符或ASCII字符，不允许中文
const nameValidator = (value: any) => {
  // 允许的字符：A-Za-z0-9!@#$%\-_=+，长度1-32，不允许空格和中文
  const nameRegex = /^[\w!@#$%\-=+]{1,32}$/
  if (!value)
    return t('NetworkConfig.LAN.Required')
  if (!nameRegex.test(value))
    return t('NetworkConfig.DHCP.InvalidDeviceName')

  return true
}

const ipv4Validator = (value: any) => {
  const ipRegex = /^(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/

  return ipRegex.test(value) || t('NetworkConfig.DHCP.InvalidIPv4')
}

const macValidator = (value: any) => {
  const macRegex = /^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/i

  return macRegex.test(value) || t('NetworkConfig.DHCP.InvalidMAC')
}

const showAdd = () => {
  isDialogVisible.value = true
  IPInfo.value = {
    static_name: '',
    static_ip: '',
    static_mac: '',
  }
}
</script>

<template>
  <div class="d-flex align-start bg-primary-transparent pa-4 rounded border border-primary mb-4">
    <VIcon
      icon="tabler-alert-circle"
      class="mr-6"
    />
    <ul class="text-primary">
      <li>
        {{ t('NetworkConfig.DHCP.DHCPBindingDesc') }}
        <div>{{ t('NetworkConfig.DHCP.DHCPBindingNote') }}</div>
      </li>
    </ul>
  </div>
  <VCard
    class="mb-5"
    :title="t('NetworkConfig.DHCP.Title')"
  >
    <template #append>
      <VBtn
        color="primary"
        @click="showAdd"
      >
        {{ t('NetworkConfig.DHCP.AddDevice') }}
      </VBtn>
    </template>
    <VCardText>
      <DhcpList ref="dhcpRef" />
    </VCardText>
  </VCard>
  <VDialog
    v-model="isDialogVisible"
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="isDialogVisible = false" />
    <VCard class="share-project-dialog pa-2 pa-sm-10">
      <h6
        class="text-lg font-weight-medium"
        style="text-align: center;"
      >
        {{ t('NetworkConfig.DHCP.AddDHCPBinding') }}
      </h6>
      <VCardText>
        <VForm ref="dhcpIpForm">
          <VRow class="match-height">
            <!-- 👉 AC信息 -->
            <VCol cols="12">
              <AppTextField
                v-model="IPInfo.static_name"
                :label="t('NetworkConfig.DHCP.DeviceName')"
                :placeholder="t('NetworkConfig.DHCP.DeviceNamePlaceholder')"
                :rules="[requiredValidator, nameValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="IPInfo.static_ip"
                :label="t('NetworkConfig.DHCP.IPAddress')"
                :placeholder="t('NetworkConfig.DHCP.IPAddressPlaceholder')"
                :rules="[requiredValidator, ipv4Validator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="IPInfo.static_mac"
                :label="t('NetworkConfig.DHCP.MACAddress')"
                :placeholder="t('NetworkConfig.DHCP.MACAddressPlaceholder')"
                :rules="[requiredValidator, macValidator]"
              />
            </VCol>
            <VCol cols="12">
              <div style="display: flex;justify-content: center;">
                <VBtn
                  color="primary"
                  class="mr-2"
                  @click="submitIP"
                >
                  {{ t('NetworkConfig.DHCP.Add') }}
                </VBtn>
                <VBtn
                  color="secondary"
                  @click="isDialogVisible = false"
                >
                  {{ t('NetworkConfig.DHCP.Cancel') }}
                </VBtn>
              </div>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss" scoped>
.blueArea {
  p {
    font-family: "PingFang SC";
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;

    /* 160% */
    margin-block-end: 0;
  }

  .bold {
    font-family: "PingFang SC";
    font-size: 15px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }

  .text {
    position: relative;
  }

  .text::before {
    position: absolute;
    border-radius: 3px;
    block-size: 6px;
    content: "";
    inline-size: 6px;
    inset-block-start: 9px;
    inset-inline-start: 9px;
  }
}
</style>
